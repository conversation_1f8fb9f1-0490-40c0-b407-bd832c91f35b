version: '3.8'

networks:
  ppg_network:
    driver: bridge

services:
  db:
    image: postgres:15
    restart: always
    environment:
      POSTGRES_DB: postgres
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - db_data:/var/lib/postgresql/data
      - ./schema.sql:/docker-entrypoint-initdb.d/schema.sql
    networks:
      - ppg_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 5s
      timeout: 5s
      retries: 5

  api:
    build:
      context: .
      dockerfile: API.Dockerfile
    restart: unless-stopped
    environment:
      - DATABASE_URL=**************************************/postgres
      - API_HOST=0.0.0.0
      - API_PORT=8000
      - API_WORKERS=1
      - LOG_LEVEL=INFO
    ports:
      - "8000:8000"
    depends_on:
      db:
        condition: service_healthy
    volumes:
      - /dev/shm:/dev/shm
    networks:
      - ppg_network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://api:8000/health || exit 1"]
      interval: 10s
      timeout: 5s
      retries: 5

  scraper:
    build:
      context: .
      dockerfile: Scraper.Dockerfile
    restart: unless-stopped
    environment:
      - DATABASE_URL=**************************************/postgres
      - PUMP_URL=https://pump.fun/coin/EpxanDRMd9iDEYdozB2CBV6tuS5mnYqBLuXrRbNcpump
      - HEADLESS=true
      - INTERVAL=5
      - ENABLE_JSON_SCRAPING=true
    depends_on:
      db:
        condition: service_healthy
      api:
        condition: service_healthy
    volumes:
      - /dev/shm:/dev/shm
    networks:
      - ppg_network

  websocket:
    build:
      context: .
      dockerfile: Websocket.Dockerfile
    restart: unless-stopped
    environment:
      - DATABASE_URL=**************************************/postgres
      - ROOM_ID=EpxanDRMd9iDEYdozB2CBV6tuS5mnYqBLuXrRbNcpump
      - PUMP_WS=wss://livechat.pump.fun/socket.io/?EIO=4&transport=websocket
      - API_URL=http://api:8000/chat
    depends_on:
      db:
        condition: service_healthy
      api:
        condition: service_healthy
    networks:
      - ppg_network

volumes:
  db_data: