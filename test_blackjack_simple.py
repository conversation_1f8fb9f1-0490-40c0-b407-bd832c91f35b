#!/usr/bin/env python3
"""
Simple test script to verify blackjack game works with only hit/stand actions
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from api.games.blackjack.game import BlackjackGame
from api.games.base import GamePhase

def test_blackjack_simple():
    """Test basic blackjack functionality with only hit/stand"""
    print("Testing simplified blackjack game...")
    
    # Create game instance
    game = BlackjackGame()
    print(f"Game created. Phase: {game.state.phase}")
    
    # Test valid actions - should only be hit and stand
    valid_actions = game.get_valid_actions()
    print(f"Valid actions when not playing: {valid_actions}")
    assert valid_actions == [], "Should have no valid actions when not in playing phase"
    
    # Start join phase
    game.state.phase = GamePhase.JOINING
    game.state.joined_players = ["test_player"]
    
    # End join phase and start game
    import asyncio
    async def test_game():
        await game.end_join_phase()
        print(f"Game phase after join: {game.state.phase}")
        
        # Check valid actions during play
        valid_actions = game.get_valid_actions()
        print(f"Valid actions during play: {valid_actions}")
        expected_actions = ["hit", "stand"]
        assert set(valid_actions) == set(expected_actions), f"Expected {expected_actions}, got {valid_actions}"
        
        # Test game state
        print(f"Player cards: {[f'{card.rank}{card.suit}' for card in game.state.player_cards]}")
        print(f"Player score: {game.state.player_score}")
        print(f"Dealer cards: {[f'{card.rank}{card.suit}' for card in game.state.dealer_cards]}")
        print(f"Dealer score: {game.state.dealer_score}")
        
        # Test hit action
        if game.state.player_score < 21:
            print("\nTesting HIT action...")
            game_continues = game.execute_action("hit")
            print(f"Game continues after hit: {game_continues}")
            print(f"New player score: {game.state.player_score}")
            print(f"Player cards: {[f'{card.rank}{card.suit}' for card in game.state.player_cards]}")
        
        # Test stand action (this should end the game)
        if game.state.game_result is None:
            print("\nTesting STAND action...")
            game_continues = game.execute_action("stand")
            print(f"Game continues after stand: {game_continues}")
            print(f"Final result: {game.state.game_result}")
            print(f"Final player score: {game.state.player_score}")
            print(f"Final dealer score: {game.state.dealer_score}")
        
        # Test that double/split actions are not available
        try:
            game.execute_action("double")
            print("ERROR: Double action should not be available!")
        except ValueError as e:
            print(f"✓ Double action correctly rejected: {e}")
        
        try:
            game.execute_action("split")
            print("ERROR: Split action should not be available!")
        except ValueError as e:
            print(f"✓ Split action correctly rejected: {e}")
    
    # Run the async test
    asyncio.run(test_game())
    print("\n✓ All tests passed! Simplified blackjack is working correctly.")

if __name__ == "__main__":
    test_blackjack_simple()
