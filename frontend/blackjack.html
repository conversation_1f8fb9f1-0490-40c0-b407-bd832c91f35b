<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pump Plays Blackjack - Live Stream</title>

    <!-- ABC Favorit Mono Font -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600;700&display=swap"
        rel="stylesheet">

    <!-- Popup CSS -->
    <link rel="stylesheet" href="popup.css">

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'JetBrains Mono', monospace;
            width: 100vw;
            height: 100vh;
            max-width: 1280px;
            max-height: 720px;
            background: url('assets/felt.png') center/cover;
            position: relative;
            overflow: hidden;
            color: #ffffff;
            font-weight: 500;
            margin: 0 auto;
        }

        /* Chat Panel - Overlay on left side */
        #chat-panel {
            position: absolute;
            top: 0;
            left: 0;
            width: 25%;
            height: 100%;
            z-index: 100;
            padding: 15px;
            padding-bottom: 25px;
            /* Extra padding to prevent cutoff */
            box-sizing: border-box;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        #chat-messages {
            flex: 1;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            gap: 8px;
            justify-content: flex-end;
            /* Align messages to bottom */
        }

        .chat-message {
            font-size: 16px;
            line-height: 1.3;
            color: #000000;
            word-wrap: break-word;
        }

        .chat-message.join {
            color: #00ff88;
        }

        .chat-message.vote {
            color: #000000;
        }

        .chat-message.action {
            color: #ffaa44;
            font-weight: 600;
        }

        .chat-message.game-event {
            color: #ff8844;
            font-weight: 600;
        }

        /* Game Table Layout - Back to original */
        #game-container {
            width: 100%;
            height: 100%;
            position: relative;
        }

        /* Dealer Area */
        #dealer-area {
            position: absolute;
            top: 50px;
            left: 50%;
            transform: translateX(-50%);
            width: 600px;
            height: 150px;
        }

        #dealer-cards {
            position: relative;
            width: 100%;
            height: 100%;
        }

        #dealer-score {
            position: absolute;
            top: -30px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.7);
            padding: 5px 15px;
            border-radius: 15px;
            font-size: 16px;
            font-weight: 600;
        }

        /* Player Area */
        #player-area {
            position: absolute;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            width: 600px;
            height: 180px;
        }

        #player-cards {
            position: relative;
            width: 100%;
            height: 100%;
        }

        #player-score {
            position: absolute;
            top: -30px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.7);
            padding: 5px 15px;
            border-radius: 15px;
            font-size: 18px;
            font-weight: 600;
        }



        /* Voting Bubbles */
        #voting-area {
            position: absolute;
            bottom: 45%;
            right: 50px;
            transform: translateY(50%);
            width: 400px;
            height: 200px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            gap: 20px;
        }

        /* Logo and text area above the circles */
        .voting-header {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
            margin-bottom: 50px;
        }

        .voting-logo {
            width: 64px;
            height: 64px;
            opacity: 0.9;
        }

        .voting-text {
            color: #FFD700;
            font-size: 20px;
            font-weight: 600;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
        }

        /* Container for the circular voting bubbles */
        .voting-circles {
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
            gap: 40px;
        }

        .vote-bubble {
            background: rgba(0, 0, 0, 0.8);
            border: 2px solid #FFD700;
            border-radius: 50%;
            text-align: center;
            font-weight: 600;
            transition: all 0.3s ease;
            width: 80px;
            height: 80px;
            position: relative;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            min-width: 80px;
            min-height: 80px;
        }

        /* Make the join bubble larger by default */
        #join-bubble {
            width: 120px;
            height: 120px;
            min-width: 120px;
            min-height: 120px;
            font-size: 35px;
        }

        .vote-bubble.flash {
            animation: obviousFlash 1.5s ease-out;
        }

        @keyframes obviousFlash {
            0% {
                background: #FFD700;
                border-color: #FFF;
                box-shadow: 0 0 30px #FFD700;
                color: #000;
            }

            15% {
                background: #FFF;
                border-color: #FFD700;
                box-shadow: 0 0 40px #FFF;
                color: #000;
            }

            30% {
                background: #FFD700;
                border-color: #FFF;
                box-shadow: 0 0 30px #FFD700;
                color: #000;
            }

            45% {
                background: #FFF;
                border-color: #FFD700;
                box-shadow: 0 0 40px #FFF;
                color: #000;
            }

            60% {
                background: #FFD700;
                border-color: #FFF;
                box-shadow: 0 0 30px #FFD700;
                color: #000;
            }

            75% {
                background: #FFF;
                border-color: #FFD700;
                box-shadow: 0 0 40px #FFF;
                color: #000;
            }

            90% {
                background: #FFD700;
                border-color: #FFF;
                box-shadow: 0 0 20px #FFD700;
                color: #000;
            }

            100% {
                background: rgba(0, 0, 0, 0.8);
                border-color: #FFD700;
                box-shadow: none;
                color: #FFF;
            }
        }

        .vote-bubble .action-name {
            font-size: 14px;
            margin-bottom: 3px;
        }

        .vote-bubble .vote-count {
            font-size: 12px;
            color: #FFD700;
        }

        /* Game Status */
        #game-status {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            padding: 10px 15px;
            border-radius: 10px;
            font-size: 14px;
        }

        #connection-status {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            padding: 10px 15px;
            border-radius: 10px;
            font-size: 14px;
            display: none;
            /* Hidden by default, shown only when disconnected */
        }

        .connected {
            color: #00ff00;
        }

        .disconnected {
            color: #ff0000;
        }

        /* Deck Area - positioned to the left of dealer's hand */
        #deck-area {
            position: absolute;
            top: 50px;
            left: 200px;
            /* Left side of dealer area */
            width: 100px;
            height: 150px;
        }

        /* Hidden elements for initial state */
        .hidden {
            display: none;
        }

        /* Animation classes */
        .dealing {
            animation: dealCard 0.5s ease-out;
        }

        @keyframes dealCard {
            from {
                transform: translate(0, 0) scale(1);
                opacity: 1;
            }

            to {
                transform: translate(var(--deal-x), var(--deal-y)) scale(1);
                opacity: 1;
            }
        }

        .shuffle-animation {
            animation: shuffle 1s ease-in-out;
        }

        @keyframes shuffle {

            0%,
            100% {
                transform: translateY(0);
            }

            25% {
                transform: translateY(-10px) rotateZ(5deg);
            }

            50% {
                transform: translateY(0) rotateZ(0deg);
            }

            75% {
                transform: translateY(-10px) rotateZ(-5deg);
            }
        }


        /* Custom card styling - standardized size for all cards */
        .custom-card {
            border-radius: 8px !important;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3) !important;
            transition: all 0.3s ease !important;
            /* Force exact same size for ALL cards - dealer and player - 50% larger */
            width: 113px !important;
            height: 150px !important;
            min-width: 113px !important;
            max-width: 113px !important;
            min-height: 150px !important;
            max-height: 150px !important;
        }

        .custom-card:hover {
            transform: translateY(-2px) !important;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4) !important;
        }

        .card-inner {
            perspective: 1000px;
        }

        .card-front,
        .card-back {
            backface-visibility: hidden;
            -webkit-backface-visibility: hidden;
            -moz-backface-visibility: hidden;
        }

        .card-back {
            transform: rotateY(180deg);
        }

        /* Game End Modal Styling */
        .game-end-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }

        .game-end-modal.show {
            display: flex;
        }

        .game-end-content {
            font-family: 'JetBrains Mono', monospace;
            text-align: center;
            color: #1D3934;
        }

        .game-end-title {
            font-size: 48px;
            font-weight: 700;
            margin-bottom: 20px;
            text-transform: uppercase;
        }

        .game-end-description {
            font-size: 18px;
            font-weight: 500;
            line-height: 1.4;
        }

        /* Responsive adjustments for smaller screens */
        @media (max-width: 1280px) {
            body {
                transform: scale(0.8);
                transform-origin: top left;
            }
        }
    </style>
</head>

<body>
    <!-- Chat Panel -->
    <div id="chat-panel">
        <div id="chat-messages">
            <!-- Chat messages will be added here dynamically -->
        </div>
    </div>

    <div id="game-container">
        <!-- Connection Status -->
        <div id="connection-status">
            <div>WebSocket: <span id="ws-status" class="disconnected">DISCONNECTED</span></div>
        </div>

        <!-- Game Status -->
        <div id="game-status">
            <div>Phase: <span id="game-phase">WAITING</span></div>
            <div>Mode: <span id="voting-mode">-</span></div>
            <div>Players: <span id="player-count">0</span></div>
        </div>

        <!-- Deck Area -->
        <div id="deck-area">
            <!-- Cards.js deck will be rendered here -->
        </div>

        <!-- Dealer Area -->
        <div id="dealer-area">
            <div id="dealer-score" class="hidden">Dealer: 0</div>
            <div id="dealer-cards">
                <!-- Dealer cards will be positioned here -->
            </div>
        </div>

        <!-- Player Area -->
        <div id="player-area">
            <div id="player-score" class="hidden">Player: 0</div>
            <div id="player-cards">
                <!-- Player cards will be positioned here -->
            </div>
        </div>

        <!-- Voting Area -->
        <div id="voting-area" class="hidden">
            <!-- Join Phase Display -->
            <div id="join-phase-display" class="hidden">
                <div class="voting-header">
                    <div id="join-text" class="voting-text">Type 'join' to join!</div>
                </div>
                <div class="voting-circles">
                    <div class="vote-bubble" id="join-bubble">
                        <div class="action-name">JOINED</div>
                        <div class="vote-count" id="join-count">0</div>
                    </div>
                </div>
            </div>

            <!-- Game Phase Display -->
            <div id="game-phase-display" class="hidden">
                <div class="voting-header">
                    <img id="voting-logo" class="voting-logo" src="" alt="Voting Mode" style="display: none;">
                    <div id="voting-text" class="voting-text"></div>
                </div>
                <div class="voting-circles">
                    <div class="vote-bubble" id="hit-bubble">
                        <div class="action-name">HIT</div>
                        <div class="vote-count">0</div>
                    </div>
                    <div class="vote-bubble" id="stand-bubble">
                        <div class="action-name">STAND</div>
                        <div class="vote-count">0</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Game End Modal -->
    <div id="game-end-modal" class="game-end-modal">
        <div class="popup">
            <div class="game-end-content">
                <div id="game-end-title" class="game-end-title">BLACKJACK!</div>
                <div id="game-end-description" class="game-end-description">
                    The cards aligned perfectly! Victory tastes sweet.
                </div>
            </div>
        </div>
    </div>

    <!-- Load Dependencies -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="custom-cards.js"></script>

    <!-- Sound Engine -->
    <script src="sound-engine.js"></script>

    <!-- Main Game Script -->
    <script src="blackjack-game.js"></script>
</body>

</html>