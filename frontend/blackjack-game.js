/**
 * <PERSON>ump Plays Blackjack - Main Game Logic
 * Integrates cards.js with WebSocket communication and game state management
 */

class BlackjackGame {
    constructor() {
        this.ws = null;
        this.gameState = {
            phase: 'WAITING',
            votingMode: null,
            playerCount: 0,
            playerCards: [],
            dealerCards: [],
            playerScore: 0,
            dealerScore: 0,
            currentVotes: {},
            eligiblePlayers: []
        };

        // Cards.js objects
        this.deck = null;
        this.playerCardSlots = []; // Individual card positions
        this.dealerCardSlots = []; // Individual card positions
        this.discardPile = null;

        // DOM elements
        this.wsStatus = document.getElementById('ws-status');
        this.connectionStatus = document.getElementById('connection-status');
        this.gamePhase = document.getElementById('game-phase');
        this.votingMode = document.getElementById('voting-mode');
        this.playerCount = document.getElementById('player-count');
        this.playerScore = document.getElementById('player-score');
        this.dealerScore = document.getElementById('dealer-score');
        this.votingArea = document.getElementById('voting-area');
        this.joinPhaseDisplay = document.getElementById('join-phase-display');
        this.gamePhaseDisplay = document.getElementById('game-phase-display');
        this.joinBubble = document.getElementById('join-bubble');
        this.joinCount = document.getElementById('join-count');
        this.hitBubble = document.getElementById('hit-bubble');
        this.standBubble = document.getElementById('stand-bubble');
        this.votingLogo = document.getElementById('voting-logo');
        this.votingText = document.getElementById('voting-text');
        this.gameEndModal = document.getElementById('game-end-modal');
        this.gameEndTitle = document.getElementById('game-end-title');
        this.gameEndDescription = document.getElementById('game-end-description');

        // Chat elements
        this.chatMessages = document.getElementById('chat-messages');
        this.maxChatMessages = 15; // Keep fewer messages due to larger font

        this.initializeCards();
        this.connectWebSocket();

        console.log('Blackjack game initialized');
    }



    initializeCards() {
        // Initialize custom card system with our game container
        cards.init({
            table: '#game-container',
            cardSize: {
                width: 113,
                height: 150,
                padding: 18
            },
            animationSpeed: 500
        });

        // Create deck - positioned to the left of dealer's hand
        this.deck = new cards.Deck({
            x: 385, // Left side, matching CSS deck-area position
            y: 35, // Same height as dealer area center
            faceUp: false
        });

        // Add all cards to deck
        this.deck.addCards(cards.all);

        // Create individual card slots for player - centered and spaced evenly
        // Player area center: 640px (50% of 1280px), Player area center Y: 600px
        const playerAreaCenterX = 780;
        const playerAreaCenterY = 530;
        const cardSpacing = 131; // Space between cards (card width 113 + padding 18)
        const playerPositions = [
            { x: playerAreaCenterX - cardSpacing * 2, y: playerAreaCenterY }, // First card
            { x: playerAreaCenterX - cardSpacing, y: playerAreaCenterY },     // Second card
            { x: playerAreaCenterX, y: playerAreaCenterY },                   // Third card (center)
            { x: playerAreaCenterX + cardSpacing, y: playerAreaCenterY },     // Fourth card
            { x: playerAreaCenterX + cardSpacing * 2, y: playerAreaCenterY }  // Fifth card
        ];

        this.playerCardSlots = playerPositions.map((pos, index) => {
            return new cards.Deck({
                x: pos.x,
                y: pos.y,
                faceUp: true
            });
        });

        // Create individual card slots for dealer - centered and spaced evenly
        // Dealer area center: 640px (50% of 1280px), Dealer area center Y: 125px
        const dealerAreaCenterX = 720;
        const dealerAreaCenterY = 35;
        const dealerPositions = [
            { x: dealerAreaCenterX - cardSpacing * 1.5, y: dealerAreaCenterY }, // First card
            { x: dealerAreaCenterX - cardSpacing * 0.5, y: dealerAreaCenterY }, // Second card
            { x: dealerAreaCenterX + cardSpacing * 0.5, y: dealerAreaCenterY }, // Third card
            { x: dealerAreaCenterX + cardSpacing * 1.5, y: dealerAreaCenterY }  // Fourth card
        ];

        this.dealerCardSlots = dealerPositions.map((pos, index) => {
            return new cards.Deck({
                x: pos.x,
                y: pos.y,
                faceUp: true
            });
        });

        // Create discard pile (for used cards)
        this.discardPile = new cards.Pile({
            x: 200, // Left side, below deck
            y: 300, // Lower on screen
            faceUp: true
        });

        // Render initial deck
        this.deck.render({ immediate: true });

        console.log('Custom card system initialized with individual card positions');
        console.log('Deck has', this.deck.length, 'cards');
        console.log('First few cards:', this.deck.cards.slice(0, 3).map(c => `${c.rank}_${c.suit}`));
    }

    connectWebSocket() {
        const wsUrl = 'ws://localhost:8000/ws';
        this.ws = new WebSocket(wsUrl);

        this.ws.onopen = (event) => {
            console.log('WebSocket connected');
            this.wsStatus.textContent = 'CONNECTED';
            this.wsStatus.className = 'connected';

            // Hide connection status when connected
            this.connectionStatus.style.display = 'none';

            // Request initial game state
            this.sendMessage({ type: 'get_game_state' });
        };

        this.ws.onmessage = (event) => {
            const data = JSON.parse(event.data);
            this.handleMessage(data);
        };

        this.ws.onclose = (event) => {
            console.log('WebSocket disconnected');
            this.wsStatus.textContent = 'DISCONNECTED';
            this.wsStatus.className = 'disconnected';

            // Show connection status when disconnected
            this.connectionStatus.style.display = 'block';

            // Attempt to reconnect after 3 seconds
            setTimeout(() => this.connectWebSocket(), 3000);
        };

        this.ws.onerror = (error) => {
            console.error('WebSocket error:', error);
        };
    }

    sendMessage(message) {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            this.ws.send(JSON.stringify(message));
        }
    }

    handleMessage(data) {
        console.log('Received message:', data);

        switch (data.type) {
            case 'connection_established':
                this.handleConnectionEstablished(data.data);
                break;
            case 'game_event':
                this.handleGameEvent(data);
                break;
            case 'game_state':
                this.updateGameState(data.data);
                break;
            case 'pong':
                // Handle ping/pong
                break;
            default:
                console.log('Unknown message type:', data.type);
        }
    }

    handleConnectionEstablished(data) {
        if (data.game_state) {
            this.updateGameState(data.game_state);
        }
        console.log('Connection established, initial state received');
    }

    handleGameEvent(data) {
        const eventType = data.event_type;
        const eventData = data.data;

        console.log('Game event:', eventType, eventData);

        switch (eventType) {
            case 'join_phase_started':
                this.handleJoinPhaseStarted(eventData);
                break;
            case 'player_joined':
                this.handlePlayerJoined(eventData);
                break;
            case 'game_started':
                this.handleGameStarted(eventData);
                break;
            case 'cards_dealt':
                this.handleCardsDealt(eventData);
                break;
            case 'turn_started':
                this.handleTurnStarted(eventData);
                break;
            case 'vote_cast':
                this.handleVoteCast(eventData);
                break;
            case 'turn_ended':
                this.handleTurnEnded(eventData);
                break;
            case 'action_executed':
                this.handleActionExecuted(eventData);
                break;
            case 'auto_stand':
                this.handleAutoStand(eventData);
                break;
            case 'game_ended':
                this.handleGameEnded(eventData);
                break;
            default:
                console.log('Unhandled game event:', eventType);
        }

        // Update game state if provided
        if (data.game_state) {
            this.updateGameState(data.game_state);
        }

        // Add chat message for relevant events
        this.addChatMessage(eventType, eventData);
    }

    updateGameState(gameState) {
        if (!gameState) return;

        // Update basic game info
        if (gameState.phase) {
            this.gameState.phase = gameState.phase;
            this.gamePhase.textContent = gameState.phase.toUpperCase();
        }

        // Update blackjack specific state
        if (gameState.game_state) {
            const state = gameState.game_state;

            if (state.voting_mode) {
                this.gameState.votingMode = state.voting_mode;
                this.votingMode.textContent = state.voting_mode.toUpperCase();
                this.updateVotingModeDisplay(state.voting_mode);
            }

            if (state.joined_players) {
                this.gameState.playerCount = state.joined_players.length;
                this.playerCount.textContent = state.joined_players.length;
            }

            if (state.current_votes) {
                this.updateVotingBubbles(state.current_votes);
            }
        }
    }

    handleJoinPhaseStarted(eventData) {
        this.gameState.phase = 'JOINING';
        this.gamePhase.textContent = 'JOINING';

        // Show voting area with join phase display
        this.votingArea.classList.remove('hidden');
        this.showJoinPhaseDisplay();

        // Reset card displays
        this.resetCardDisplay();

        // Play game start sound
        if (window.soundEngine) {
            window.soundEngine.playGameEvent('start');
        }
    }

    handleGameStarted(eventData) {
        this.gameState.phase = 'PLAYING';
        this.gamePhase.textContent = 'PLAYING';

        if (eventData.voting_mode) {
            this.gameState.votingMode = eventData.voting_mode;
            this.votingMode.textContent = eventData.voting_mode.toUpperCase();
            this.updateVotingModeDisplay(eventData.voting_mode);
        }

        if (eventData.player_count) {
            this.gameState.playerCount = eventData.player_count;
            this.playerCount.textContent = eventData.player_count;
        }

        // Hide join phase display and show game phase display
        this.showGamePhaseDisplay();

        // Hide voting area initially - it will be shown during turns
        this.votingArea.classList.add('hidden');

        // Shuffle deck animation
        this.animateShuffleDeck();
    }

    handleCardsDealt(eventData) {
        // Show score displays
        this.playerScore.classList.remove('hidden');
        this.dealerScore.classList.remove('hidden');

        // Update cards and scores
        if (eventData.player_cards) {
            this.updatePlayerCards(eventData.player_cards);
            this.playerScore.textContent = `Player: ${eventData.player_score || 0}`;
        }

        if (eventData.dealer_cards) {
            this.updateDealerCards(eventData.dealer_cards);
            this.dealerScore.textContent = `Dealer: ${eventData.dealer_score || 0}`;
        }

        // Animate dealing cards
        this.animateCardDealing();
    }

    handleTurnStarted(eventData) {
        // Show voting area with game phase display
        this.votingArea.classList.remove('hidden');
        this.showGamePhaseDisplay();

        // Update voting mode display if available
        if (this.gameState.votingMode) {
            this.updateVotingModeDisplay(this.gameState.votingMode);
        }

        // Reset vote counts immediately to 0
        this.resetVotingBubbles();
    }

    handleVoteCast(eventData) {
        // Play vote sound
        if (window.soundEngine) {
            window.soundEngine.playVoteCast();
        }

        // Request updated game state to get current votes
        this.sendMessage({ type: 'get_game_state' });
    }

    handleTurnEnded(eventData) {
        if (eventData.votes) {
            this.updateVotingBubbles(eventData.votes);
        }

        // Hide voting area after a delay
        setTimeout(() => {
            this.votingArea.classList.add('hidden');
        }, 2000);
    }

    handleActionExecuted(eventData) {
        // Flash the button that corresponds to the executed action
        if (eventData.action === 'hit') {
            this.flashButton(this.hitBubble);
        } else if (eventData.action === 'stand') {
            this.flashButton(this.standBubble);
        }

        // Reset vote counts immediately when action is executed
        this.resetVotingBubbles();

        // Update cards if new ones were dealt
        if (eventData.player_cards) {
            this.updatePlayerCards(eventData.player_cards);
            this.playerScore.textContent = `Player: ${eventData.player_score || 0}`;
        }

        if (eventData.dealer_cards) {
            this.updateDealerCards(eventData.dealer_cards);
            this.dealerScore.textContent = `Dealer: ${eventData.dealer_score || 0}`;
        }

        // Play card deal sound for new cards
        if (window.soundEngine) {
            window.soundEngine.playCardDeal();
        }
    }

    handleAutoStand(eventData) {
        // Player reached 21 and automatically stood
        console.log('Auto stand triggered:', eventData.reason);

        // Hide voting area since no votes are needed
        this.votingArea.classList.add('hidden');

        // Reset vote counts
        this.resetVotingBubbles();

        // Update player score display
        if (eventData.player_score) {
            this.playerScore.textContent = `Player: ${eventData.player_score}`;
        }
    }

    handleGameEnded(eventData) {
        this.gameState.phase = 'FINISHED';
        this.gamePhase.textContent = 'FINISHED';

        // Hide voting area
        this.votingArea.classList.add('hidden');

        // Add delay before showing game end modal to let card animations finish
        setTimeout(() => {
            this.showGameEndModal(eventData.result);
        }, 1500); // 1.5 second delay

        // Play appropriate end game sound after a shorter delay
        setTimeout(() => {
            if (window.soundEngine && eventData.result) {
                const gameResult = eventData.result.result; // Use correct field name
                switch (gameResult) {
                    case 'blackjack':
                        window.soundEngine.playGameEvent('blackjack');
                        break;
                    case 'player_busted':
                    case 'dealer_busted':
                        window.soundEngine.playGameEvent('bust');
                        break;
                    case 'player_win':
                        window.soundEngine.playGameEvent('win');
                        break;
                    case 'dealer_win':
                        window.soundEngine.playGameEvent('lose');
                        break;
                    case 'push':
                        // Could add a specific push sound, or use win/neutral sound
                        window.soundEngine.playGameEvent('win');
                        break;
                    default:
                        // Fallback to old logic if result not available
                        if (eventData.result.reason && eventData.result.reason.includes('blackjack')) {
                            window.soundEngine.playGameEvent('blackjack');
                        } else if (eventData.result.reason && eventData.result.reason.includes('bust')) {
                            window.soundEngine.playGameEvent('bust');
                        } else if (eventData.result.winner === 'player') {
                            window.soundEngine.playGameEvent('win');
                        } else {
                            window.soundEngine.playGameEvent('lose');
                        }
                }
            }
        }, 1000); // 1 second delay for sound
    }

    updatePlayerCards(cards) {
        // Track existing cards to avoid redealing them
        const currentCardCount = this.gameState.playerCards ? this.gameState.playerCards.length : 0;

        // If we have fewer cards than before, it's a new game - clear all slots
        if (cards.length < currentCardCount) {
            this.playerCardSlots.forEach(slot => {
                // Move any cards back to deck
                while (slot.length > 0) {
                    const card = slot.pop();
                    this.deck.addCard(card, true); // Animate return to deck
                }
                slot.render({ immediate: true });
            });
        }

        // Only deal new cards (cards beyond the current count)
        for (let index = currentCardCount; index < cards.length; index++) {
            if (index < this.playerCardSlots.length) {
                const cardData = cards[index];
                const card = this.findCardByData(cardData);
                if (card) {
                    // Remove card from deck first
                    const deckIndex = this.deck.cards.indexOf(card);
                    if (deckIndex > -1) {
                        this.deck.cards.splice(deckIndex, 1);
                    }

                    // Deal with animation and flip
                    setTimeout(() => {
                        window.customCardSystem.dealCard(
                            card,
                            this.deck.x,
                            this.deck.y,
                            this.playerCardSlots[index].x,
                            this.playerCardSlots[index].y,
                            true
                        );

                        // Add to slot after animation
                        setTimeout(() => {
                            this.playerCardSlots[index].addCard(card, false);
                        }, window.customCardSystem.animationSpeed);
                    }, (index - currentCardCount) * 200); // Stagger only new cards
                }
            }
        }

        // Update game state to track current cards
        this.gameState.playerCards = [...cards];
    }

    updateDealerCards(cards) {
        // Track existing cards to avoid redealing them
        const currentCardCount = this.gameState.dealerCards ? this.gameState.dealerCards.length : 0;

        // If we have fewer cards than before, it's a new game - clear all slots
        if (cards.length < currentCardCount) {
            this.dealerCardSlots.forEach(slot => {
                // Move any cards back to deck
                while (slot.length > 0) {
                    const card = slot.pop();
                    this.deck.addCard(card, true); // Animate return to deck
                }
                slot.render({ immediate: true });
            });
        }

        // Only deal new cards (cards beyond the current count)
        for (let index = currentCardCount; index < cards.length; index++) {
            if (index < this.dealerCardSlots.length) {
                const cardData = cards[index];
                const card = this.findCardByData(cardData);
                if (card) {
                    // Remove card from deck first
                    const deckIndex = this.deck.cards.indexOf(card);
                    if (deckIndex > -1) {
                        this.deck.cards.splice(deckIndex, 1);
                    }

                    // For dealer, first card face up, second card face down initially
                    const shouldFlip = index === 0 || cardData.visible !== false;

                    // Deal with animation
                    setTimeout(() => {
                        window.customCardSystem.dealCard(
                            card,
                            this.deck.x,
                            this.deck.y,
                            this.dealerCardSlots[index].x,
                            this.dealerCardSlots[index].y,
                            shouldFlip
                        );

                        // Add to slot after animation
                        setTimeout(() => {
                            this.dealerCardSlots[index].addCard(card, false);
                        }, window.customCardSystem.animationSpeed);
                    }, (index - currentCardCount) * 200); // Stagger only new cards
                }
            }
        }

        // Update game state to track current cards
        this.gameState.dealerCards = [...cards];
    }

    findCardByData(cardData) {
        // Convert API card format to cards.js format
        const suitMap = {
            'hearts': 'h',
            'diamonds': 'd',
            'clubs': 'c',
            'spades': 's'
        };

        const rankMap = {
            'A': 1, '2': 2, '3': 3, '4': 4, '5': 5, '6': 6, '7': 7, '8': 8, '9': 9, '10': 10,
            'J': 11, 'Q': 12, 'K': 13
        };

        const suit = suitMap[cardData.suit];
        const rank = rankMap[cardData.rank];

        if (!suit || !rank) {
            console.warn('Invalid card data:', cardData);
            return null;
        }

        // Find the card in the deck
        return cards.all.find(card => card.suit === suit && card.rank === rank);
    }

    updateVotingBubbles(votes) {
        // Update HIT bubble
        const hitCount = votes.hit ? (Array.isArray(votes.hit) ? votes.hit.length : votes.hit) : 0;
        this.hitBubble.querySelector('.vote-count').textContent = hitCount;

        // Update STAND bubble
        const standCount = votes.stand ? (Array.isArray(votes.stand) ? votes.stand.length : votes.stand) : 0;
        this.standBubble.querySelector('.vote-count').textContent = standCount;

        // Scale bubbles based on vote count
        this.scaleBubble(this.hitBubble, hitCount);
        this.scaleBubble(this.standBubble, standCount);
    }

    resetVotingBubbles() {
        // Only reset if we're in game phase (not join phase)
        if (this.hitBubble && this.standBubble) {
            // Reset vote counts to 0 immediately
            this.hitBubble.querySelector('.vote-count').textContent = '0';
            this.standBubble.querySelector('.vote-count').textContent = '0';

            // Reset bubble scaling and styling
            this.scaleBubble(this.hitBubble, 0);
            this.scaleBubble(this.standBubble, 0);
        }
    }

    scaleBubble(bubble, voteCount) {
        // Get total votes for better scaling calculation
        const hitCount = parseInt(this.hitBubble?.querySelector('.vote-count')?.textContent) || 0;
        const standCount = parseInt(this.standBubble?.querySelector('.vote-count')?.textContent) || 0;
        const totalVotes = hitCount + standCount;

        // Scale bubble size based on vote count relative to total votes
        const baseScale = 1.0;
        const maxScale = 2.0;

        let scale = baseScale;
        if (totalVotes > 0) {
            // Scale based on percentage of total votes, with minimum scaling
            const voteRatio = voteCount / totalVotes;
            scale = baseScale + (voteRatio * (maxScale - baseScale)) + (voteCount * 0.05);
            scale = Math.min(maxScale, scale);
        }

        bubble.style.transform = `scale(${scale})`;

        // Ensure proper spacing by adjusting margin based on scale
        const marginAdjustment = (scale - 1) * 20; // Increase margin as bubble grows
        bubble.style.margin = `0 ${marginAdjustment}px`;
    }

    animateShuffleDeck() {
        // Add shuffle animation to deck cards
        if (this.deck && this.deck.length > 0) {
            this.deck.cards.forEach(card => {
                if (card.element) {
                    card.element.classList.add('shuffle-animation');
                    setTimeout(() => {
                        card.element.classList.remove('shuffle-animation');
                    }, 1000);
                }
            });
        }

        // Play shuffle sound
        if (window.soundEngine) {
            window.soundEngine.playCardShuffle();
        }
    }

    animateCardDealing() {
        // This is a placeholder for card dealing animation
        // The actual card movement is handled by cards.js when we update the hands

        // Play dealing sound sequence
        if (window.soundEngine) {
            // Play multiple card deal sounds with delays
            window.soundEngine.playSequence([
                { sound: 'card_deal', delay: 0 },
                { sound: 'card_deal', delay: 300 },
                { sound: 'card_deal', delay: 600 }
            ]);
        }
    }

    resetCardDisplay() {
        // Clear all player card slots and move cards back to deck
        this.playerCardSlots.forEach(slot => {
            while (slot.length > 0) {
                const card = slot.pop();
                // Flip card face down and move back to deck
                window.customCardSystem.flipCard(card, false);
                this.deck.addCard(card, true); // Animate return to deck
            }
            slot.render({ immediate: true });
        });

        // Clear all dealer card slots and move cards back to deck
        this.dealerCardSlots.forEach(slot => {
            while (slot.length > 0) {
                const card = slot.pop();
                // Flip card face down and move back to deck
                window.customCardSystem.flipCard(card, false);
                this.deck.addCard(card, true); // Animate return to deck
            }
            slot.render({ immediate: true });
        });

        // Clear discard pile and move cards back to deck
        if (this.discardPile && this.discardPile.length > 0) {
            while (this.discardPile.length > 0) {
                const card = this.discardPile.pop();
                // Flip card face down and move back to deck
                window.customCardSystem.flipCard(card, false);
                this.deck.addCard(card, true); // Animate return to deck
            }
            this.discardPile.render({ immediate: true });
        }

        // Re-render deck after a delay to allow animations to complete
        setTimeout(() => {
            if (this.deck) {
                this.deck.render({ immediate: true });
            }
        }, window.customCardSystem.animationSpeed + 100);

        // Hide score displays
        this.playerScore.classList.add('hidden');
        this.dealerScore.classList.add('hidden');

        // Reset scores
        this.playerScore.textContent = 'Player: 0';
        this.dealerScore.textContent = 'Dealer: 0';

        // Reset game state
        this.gameState.playerCards = [];
        this.gameState.dealerCards = [];
        this.gameState.playerScore = 0;
        this.gameState.dealerScore = 0;
    }

    updateVotingModeDisplay(votingMode) {
        if (!votingMode) return;

        if (votingMode.toLowerCase() === 'anarchy') {
            this.votingLogo.src = 'assets/anarchy.png';
            this.votingLogo.style.display = 'block';
            this.votingText.textContent = 'Random move wins!';
        } else if (votingMode.toLowerCase() === 'democracy') {
            this.votingLogo.src = 'assets/democracy.png';
            this.votingLogo.style.display = 'block';
            this.votingText.textContent = 'Most voted move wins!';
        } else {
            this.votingLogo.style.display = 'none';
            this.votingText.textContent = '';
        }
    }

    flashButton(bubble) {
        // Remove any existing flash class
        bubble.classList.remove('flash');

        // Force reflow to ensure class removal is processed
        bubble.offsetHeight;

        // Add flash class to trigger animation
        bubble.classList.add('flash');

        // Remove flash class after animation completes
        setTimeout(() => {
            bubble.classList.remove('flash');
        }, 1500); // Match new animation duration
    }

    handlePlayerJoined(eventData) {
        // Only update if we have total_players data
        if (eventData.total_players !== undefined) {
            this.joinCount.textContent = eventData.total_players;

            // Scale the join bubble based on player count
            this.scaleBubble(this.joinBubble, eventData.total_players);

            // Update the main player count display as well
            this.gameState.playerCount = eventData.total_players;
            this.playerCount.textContent = eventData.total_players;
        }
    }

    showJoinPhaseDisplay() {
        this.joinPhaseDisplay.classList.remove('hidden');
        this.gamePhaseDisplay.classList.add('hidden');

        // Reset join count to current player count
        this.joinCount.textContent = this.gameState.playerCount || 0;
        this.scaleBubble(this.joinBubble, this.gameState.playerCount || 0);
    }

    showGamePhaseDisplay() {
        this.joinPhaseDisplay.classList.add('hidden');
        this.gamePhaseDisplay.classList.remove('hidden');
    }

    showGameEndModal(result) {
        if (!result) return;

        let title = '';
        let description = '';

        // Use the correct field name from websocket data
        const gameResult = result.result; // The field is called 'result' not 'game_result'

        switch (gameResult) {
            case 'player_busted':
                title = 'PLAYER BUST!';
                description = result.reason || `Player busted with ${result.player_score || 'high score'}`;
                break;
            case 'dealer_busted':
                title = 'DEALER BUST!';
                description = result.reason || `Dealer busted with ${result.dealer_score || 'high score'}`;
                break;
            case 'blackjack':
                title = 'BLACKJACK!';
                description = result.reason || 'Player got blackjack!';
                break;
            case 'push':
                title = 'PUSH!';
                description = result.reason || `Push - both have ${result.player_score || 'same score'}`;
                break;
            case 'player_win':
                title = 'YOU WIN!';
                description = result.reason || `Player wins ${result.player_score || '?'} vs ${result.dealer_score || '?'}`;
                break;
            case 'dealer_win':
                title = 'DEALER WINS!';
                description = result.reason || `Dealer wins ${result.dealer_score || '?'} vs ${result.player_score || '?'}`;
                break;
            default:
                // Fallback to old logic if game_result not recognized
                if (result.reason && result.reason.includes('blackjack')) {
                    if (result.winner === 'player') {
                        title = 'BLACKJACK!';
                        description = 'The cards aligned perfectly! Victory tastes sweet.';
                    } else {
                        title = 'DEALER BLACKJACK!';
                        description = 'The house always has tricks up its sleeve.';
                    }
                } else if (result.reason && result.reason.includes('bust')) {
                    if (result.winner === 'dealer') {
                        title = 'BUST!';
                        description = 'Too greedy! Sometimes less is more.';
                    } else {
                        title = 'DEALER BUST!';
                        description = 'The house got too confident. Your patience paid off!';
                    }
                } else if (result.winner === 'player') {
                    title = 'YOU WIN!';
                    description = 'Skill and luck combined for a perfect victory!';
                } else if (result.winner === 'dealer') {
                    title = 'DEALER WINS!';
                    description = 'The house edge strikes again. Better luck next time!';
                } else {
                    title = 'PUSH!';
                    description = 'A perfect tie! Great minds think alike.';
                }
        }

        // Update modal content
        this.gameEndTitle.textContent = title;
        this.gameEndDescription.textContent = description;

        // Show modal
        this.gameEndModal.classList.add('show');

        // Hide modal after 4 seconds
        setTimeout(() => {
            this.gameEndModal.classList.remove('show');
        }, 4000);
    }

    addChatMessage(eventType, eventData) {
        let messageText = '';
        let messageClass = '';

        switch (eventType) {
            case 'player_joined':
                // Only process if this event contains total_players (to avoid duplicates)
                if (eventData.username && eventData.total_players !== undefined) {
                    messageText = `${eventData.username} joined`;
                    messageClass = 'join';
                }
                break;

            case 'vote_cast':
                // Only process if this event contains current_votes (to avoid duplicates)
                if (eventData.username && eventData.action && eventData.current_votes !== undefined) {
                    messageText = `${eventData.username} voted ${eventData.action}`;
                    messageClass = 'vote';
                }
                break;

            case 'action_executed':
                if (eventData.action) {
                    messageText = `Action: ${eventData.action}`;
                    messageClass = 'action';
                }
                break;

            case 'game_started':
                messageText = `Game started (${eventData.voting_mode || 'unknown'} mode)`;
                messageClass = 'game-event';
                break;

            case 'game_ended':
                if (eventData.result && eventData.result.result) {
                    const formattedResult = this.formatGameResult(eventData.result.result);
                    messageText = `Game ended: ${formattedResult}`;
                    messageClass = 'game-event';
                }
                break;

            case 'join_phase_started':
                messageText = 'Join phase started';
                messageClass = 'game-event';
                break;

            default:
                // Don't show messages for other events
                return;
        }

        if (messageText) {
            this.displayChatMessage(messageText, messageClass);
        }
    }

    formatGameResult(result) {
        switch (result) {
            case 'player_busted':
                return 'Player busted';
            case 'dealer_busted':
                return 'Dealer busted';
            case 'player_win':
                return 'Player wins';
            case 'dealer_win':
                return 'Dealer wins';
            case 'blackjack':
                return 'Blackjack!';
            case 'push':
                return 'Push (tie)';
            default:
                return result;
        }
    }

    displayChatMessage(text, className = '') {
        const messageElement = document.createElement('div');
        messageElement.className = `chat-message ${className}`;
        messageElement.textContent = text;

        this.chatMessages.appendChild(messageElement);

        // Remove old messages if we exceed the limit
        while (this.chatMessages.children.length > this.maxChatMessages) {
            this.chatMessages.removeChild(this.chatMessages.firstChild);
        }

        // Check if we need to remove messages to prevent overflow
        // Use a small delay to ensure the DOM has updated
        setTimeout(() => {
            const containerHeight = this.chatMessages.offsetHeight;
            const totalMessagesHeight = this.chatMessages.scrollHeight;

            // Remove messages from the top until everything fits
            while (totalMessagesHeight > containerHeight && this.chatMessages.children.length > 1) {
                const firstMessage = this.chatMessages.firstChild;
                if (firstMessage) {
                    this.chatMessages.removeChild(firstMessage);
                }
                // Recalculate after removal
                const newTotalHeight = this.chatMessages.scrollHeight;
                if (newTotalHeight === totalMessagesHeight) {
                    // No change in height, break to prevent infinite loop
                    break;
                }
            }
        }, 10);
    }
}

// Initialize the game when the page loads
document.addEventListener('DOMContentLoaded', () => {
    window.blackjackGame = new BlackjackGame();
});
