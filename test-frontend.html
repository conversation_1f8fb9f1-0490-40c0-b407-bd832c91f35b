<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pump Plays Games - Live Stream</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            background-color: #1a1a1a;
            color: #00ff00;
            margin: 0;
            padding: 20px;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .panel {
            background-color: #2a2a2a;
            border: 2px solid #00ff00;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .panel h2 {
            color: #ffff00;
            margin-top: 0;
            border-bottom: 1px solid #00ff00;
            padding-bottom: 10px;
        }

        .status {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .connected {
            color: #00ff00;
            font-weight: bold;
        }

        .disconnected {
            color: #ff0000;
            font-weight: bold;
        }

        .game-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 15px;
        }

        .card {
            background-color: #3a3a3a;
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #555;
        }

        .card-display {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
            margin: 10px 0;
        }

        .playing-card {
            background-color: #ffffff;
            color: #000000;
            padding: 8px 12px;
            border-radius: 5px;
            font-weight: bold;
            min-width: 30px;
            text-align: center;
        }

        .hearts,
        .diamonds {
            color: #ff0000;
        }

        .clubs,
        .spades {
            color: #000000;
        }

        .players-list {
            background-color: #3a3a3a;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            max-height: 200px;
            overflow-y: auto;
        }

        .player {
            padding: 5px;
            margin: 2px 0;
            background-color: #4a4a4a;
            border-radius: 3px;
        }

        .voting {
            display: flex;
            justify-content: space-around;
            margin: 10px 0;
        }

        .vote-count {
            background-color: #4a4a4a;
            padding: 10px;
            border-radius: 5px;
            text-align: center;
        }

        .events-log {
            height: 400px;
            overflow-y: auto;
            background-color: #0a0a0a;
            padding: 10px;
            border: 1px solid #555;
            border-radius: 5px;
        }

        .event {
            margin: 5px 0;
            padding: 8px;
            border-left: 3px solid #00ff00;
            background-color: #1a1a1a;
        }

        .event-type {
            color: #ffff00;
            font-weight: bold;
        }

        .event-time {
            color: #888;
            font-size: 0.8em;
        }

        .chat-log {
            height: 300px;
            overflow-y: auto;
            background-color: #0a0a0a;
            padding: 10px;
            border: 1px solid #555;
            border-radius: 5px;
        }

        .chat-message {
            margin: 5px 0;
            padding: 5px;
        }

        .username {
            color: #00ffff;
            font-weight: bold;
        }

        .full-width {
            grid-column: 1 / -1;
        }

        .pulse {
            animation: pulse 1s infinite;
        }

        @keyframes pulse {
            0% {
                opacity: 1;
            }

            50% {
                opacity: 0.5;
            }

            100% {
                opacity: 1;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <!-- Connection Status -->
        <div class="panel">
            <h2>🔌 Connection Status</h2>
            <div class="status">
                <span>WebSocket:</span>
                <span id="ws-status" class="disconnected">DISCONNECTED</span>
            </div>
            <div class="status">
                <span>Connected Clients:</span>
                <span id="client-count">0</span>
            </div>
        </div>

        <!-- Game Status -->
        <div class="panel">
            <h2>🎮 Game Status</h2>
            <div class="game-info">
                <div class="card">
                    <strong>Game Type:</strong><br>
                    <span id="game-type">-</span>
                </div>
                <div class="card">
                    <strong>Phase:</strong><br>
                    <span id="game-phase">-</span>
                </div>
                <div class="card">
                    <strong>Voting Mode:</strong><br>
                    <span id="voting-mode">-</span>
                </div>
                <div class="card">
                    <strong>Players Joined:</strong><br>
                    <span id="player-count">0</span>
                </div>
            </div>
        </div>

        <!-- Game Board -->
        <div class="panel">
            <h2>🃏 Blackjack Table</h2>
            <div class="card">
                <strong>Player Hand (Score: <span id="player-score">0</span>):</strong>
                <div id="player-cards" class="card-display">
                    <!-- Cards will appear here -->
                </div>
            </div>
            <div class="card">
                <strong>Dealer Hand (Score: <span id="dealer-score">0</span>):</strong>
                <div id="dealer-cards" class="card-display">
                    <!-- Cards will appear here -->
                </div>
            </div>
            <div class="card">
                <strong>Available Actions:</strong>
                <div id="available-actions">-</div>
            </div>
            <div class="card" id="current-hand-indicator" style="display: none;">
                <strong>Current Hand:</strong>
                <span id="current-hand">First Hand</span>
            </div>
        </div>

        <!-- Players & Voting -->
        <div class="panel">
            <h2>👥 Players & Voting</h2>
            <div class="card">
                <strong>Eligible Players:</strong>
                <div id="eligible-players" class="players-list">
                    <!-- Players will appear here -->
                </div>
            </div>
            <div class="card">
                <strong>Current Votes:</strong>
                <div id="current-votes" class="voting">
                    <!-- Vote counts will appear here -->
                </div>
            </div>
        </div>

        <!-- Events Log -->
        <div class="panel full-width">
            <h2>📡 Live Events Stream</h2>
            <div id="events-log" class="events-log">
                <div class="event">
                    <span class="event-time">Waiting for connection...</span>
                </div>
            </div>
        </div>

        <!-- Chat Log -->
        <div class="panel full-width">
            <h2>💬 Chat Messages</h2>
            <div id="chat-log" class="chat-log">
                <div class="chat-message">
                    <span class="username">System:</span> Waiting for messages...
                </div>
            </div>
        </div>
    </div>

    <script>
        let ws;
        let eventCount = 0;

        // DOM elements
        const wsStatus = document.getElementById('ws-status');
        const clientCount = document.getElementById('client-count');
        const gameType = document.getElementById('game-type');
        const gamePhase = document.getElementById('game-phase');
        const votingMode = document.getElementById('voting-mode');
        const playerCount = document.getElementById('player-count');
        const playerScore = document.getElementById('player-score');
        const dealerScore = document.getElementById('dealer-score');
        const playerCards = document.getElementById('player-cards');
        const dealerCards = document.getElementById('dealer-cards');
        const availableActions = document.getElementById('available-actions');
        const eligiblePlayers = document.getElementById('eligible-players');
        const currentVotes = document.getElementById('current-votes');
        const eventsLog = document.getElementById('events-log');
        const chatLog = document.getElementById('chat-log');

        function connectWebSocket() {
            ws = new WebSocket('ws://localhost:8000/ws');

            ws.onopen = function (event) {
                console.log('WebSocket connected');
                wsStatus.textContent = 'CONNECTED';
                wsStatus.className = 'connected';
                addEvent('system', 'WebSocket connected successfully');
            };

            ws.onmessage = function (event) {
                const data = JSON.parse(event.data);
                handleMessage(data);
            };

            ws.onclose = function (event) {
                console.log('WebSocket closed');
                wsStatus.textContent = 'DISCONNECTED';
                wsStatus.className = 'disconnected';
                addEvent('system', 'WebSocket disconnected');

                // Reconnect after 3 seconds
                setTimeout(connectWebSocket, 3000);
            };

            ws.onerror = function (error) {
                console.log('WebSocket error:', error);
                addEvent('error', 'WebSocket error occurred');
            };
        }

        function handleMessage(data) {
            console.log('Received:', data);

            switch (data.type) {
                case 'connection_established':
                    handleConnectionEstablished(data.data);
                    break;
                case 'game_event':
                    handleGameEvent(data);
                    break;
                case 'chat_message':
                    handleChatMessage(data.data);
                    break;
                case 'game_state':
                    // Handle game state response
                    if (data.data) {
                        updateGameState(data.data);
                    }
                    break;
                case 'pong':
                    // Handle pong response from server (no action needed)
                    console.log('Received pong from server');
                    break;
                default:
                    addEvent('unknown', `Unknown message type: ${data.type}`, data);
            }
        }

        function handleConnectionEstablished(data) {
            clientCount.textContent = data.connected_clients || 0;

            if (data.game_state) {
                updateGameState(data.game_state);
            }

            if (data.recent_messages) {
                data.recent_messages.forEach(msg => {
                    addChatMessage(msg.username, msg.message, msg.timestamp);
                });
            }

            addEvent('connection', 'Initial state received');
        }

        function handleGameEvent(data) {
            const eventType = data.event_type;
            const eventData = data.data;

            addEvent(eventType, JSON.stringify(eventData), eventData);

            // Update game state based on events
            switch (eventType) {
                case 'join_phase_started':
                    gamePhase.textContent = 'JOINING';
                    break;
                case 'game_started':
                    gamePhase.textContent = 'PLAYING';
                    if (eventData.voting_mode) {
                        votingMode.textContent = eventData.voting_mode.toUpperCase();
                    }
                    if (eventData.player_count) {
                        playerCount.textContent = eventData.player_count;
                    }
                    break;
                case 'cards_dealt':
                    if (eventData.player_cards) {
                        displayCards(playerCards, eventData.player_cards);
                        playerScore.textContent = eventData.player_score || 0;
                    }
                    if (eventData.dealer_cards) {
                        displayCards(dealerCards, eventData.dealer_cards);
                        dealerScore.textContent = eventData.dealer_score || 0;
                    }
                    break;
                case 'action_executed':
                    // Handle new cards from actions like hit, stand, etc.
                    if (eventData.player_cards) {
                        displayCards(playerCards, eventData.player_cards);
                        playerScore.textContent = eventData.player_score || 0;
                    }
                    if (eventData.dealer_cards) {
                        displayCards(dealerCards, eventData.dealer_cards);
                        dealerScore.textContent = eventData.dealer_score || 0;
                    }
                    break;
                case 'dealer_draws':
                    // Handle dealer drawing cards
                    if (eventData.dealer_score) {
                        dealerScore.textContent = eventData.dealer_score;
                    }
                    break;
                case 'player_bust':
                    // Handle player bust
                    if (eventData.player_score) {
                        playerScore.textContent = eventData.player_score;
                    }
                    break;
                // TODO: Add split-related event handlers in future implementation
                // case 'hand_busted':
                // case 'hand_complete':
                case 'eligible_players_updated':
                    if (eventData.eligible_players) {
                        displayPlayers(eventData.eligible_players);
                    }
                    break;
                case 'turn_started':
                    if (eventData.valid_actions) {
                        availableActions.textContent = eventData.valid_actions.join(', ');
                    }
                    if (eventData.eligible_players) {
                        displayPlayers(eventData.eligible_players);
                    }
                    break;
                case 'vote_cast':
                    // Update vote display immediately when votes are cast
                    // We need to get the current game state to show updated votes
                    if (ws && ws.readyState === WebSocket.OPEN) {
                        ws.send(JSON.stringify({ type: 'get_game_state' }));
                    }
                    break;
                case 'turn_ended':
                    if (eventData.votes) {
                        displayVotes(eventData.votes);
                    }
                    break;
                case 'game_ended':
                    gamePhase.textContent = 'FINISHED';
                    availableActions.textContent = '-';

                    // Display game result and reason
                    if (eventData.result && eventData.result.reason) {
                        addEvent('game_result', `Game ended: ${eventData.result.reason}`, eventData.result);
                    }

                    // Clear votes display
                    currentVotes.innerHTML = '';
                    break;
                case 'next_game_scheduled':
                    // Handle next game scheduling
                    gamePhase.textContent = 'WAITING FOR NEXT GAME';
                    if (eventData.message) {
                        addEvent('next_game', eventData.message, eventData);
                    }
                    break;
                case 'join_phase_started':
                    // Handle new game starting
                    gamePhase.textContent = 'JOINING';
                    availableActions.textContent = '-';

                    // Clear previous game state
                    playerCards.innerHTML = '';
                    dealerCards.innerHTML = '';
                    playerScore.textContent = '0';
                    dealerScore.textContent = '0';
                    currentVotes.innerHTML = '';
                    eligiblePlayers.innerHTML = '';

                    if (eventData.message) {
                        addEvent('join_phase', eventData.message, eventData);
                    }
                    break;
            }

            // Generic game state update if available
            if (data.game_state) {
                updateGameState(data.game_state);
            }
        }

        function handleChatMessage(data) {
            addChatMessage(data.username, data.message, data.timestamp);
        }

        function updateGameState(gameState) {
            if (gameState.game_type) {
                gameType.textContent = gameState.game_type.toUpperCase();
            }
            if (gameState.phase) {
                gamePhase.textContent = gameState.phase.toUpperCase();
            }

            // Update blackjack specific state
            if (gameState.game_state) {
                const state = gameState.game_state;

                if (state.joined_players) {
                    playerCount.textContent = state.joined_players.length;
                }

                if (state.voting_mode) {
                    votingMode.textContent = state.voting_mode.toUpperCase();
                }

                if (state.current_votes) {
                    displayVotes(state.current_votes);
                }

                if (state.eligible_players) {
                    displayPlayers(state.eligible_players);
                }
            }
        }

        function displayCards(container, cards) {
            container.innerHTML = '';
            cards.forEach(card => {
                const cardElement = document.createElement('div');
                cardElement.className = `playing-card ${card.suit}`;
                cardElement.textContent = `${card.rank}${getSuitSymbol(card.suit)}`;
                container.appendChild(cardElement);
            });
        }

        function getSuitSymbol(suit) {
            const symbols = {
                'hearts': '♥',
                'diamonds': '♦',
                'clubs': '♣',
                'spades': '♠'
            };
            return symbols[suit] || suit;
        }

        function displayPlayers(players) {
            eligiblePlayers.innerHTML = '';
            players.forEach(player => {
                const playerElement = document.createElement('div');
                playerElement.className = 'player';
                playerElement.textContent = player;
                eligiblePlayers.appendChild(playerElement);
            });
        }

        function displayVotes(votes) {
            currentVotes.innerHTML = '';
            Object.entries(votes).forEach(([action, voters]) => {
                const voteElement = document.createElement('div');
                voteElement.className = 'vote-count';
                voteElement.innerHTML = `<strong>${action.toUpperCase()}</strong><br>${Array.isArray(voters) ? voters.length : voters}`;
                currentVotes.appendChild(voteElement);
            });
        }

        function addEvent(type, message, data = null) {
            eventCount++;
            const eventElement = document.createElement('div');
            eventElement.className = 'event';
            eventElement.innerHTML = `
                <div class="event-time">[${new Date().toLocaleTimeString()}] #${eventCount}</div>
                <div class="event-type">${type.toUpperCase()}</div>
                <div>${message}</div>
                ${data ? `<pre style="font-size: 0.8em; color: #ccc; margin-top: 5px;">${JSON.stringify(data, null, 2)}</pre>` : ''}
            `;

            eventsLog.appendChild(eventElement);
            eventsLog.scrollTop = eventsLog.scrollHeight;

            // Add pulse effect
            eventElement.classList.add('pulse');
            setTimeout(() => eventElement.classList.remove('pulse'), 1000);
        }

        function addChatMessage(username, message, timestamp) {
            const chatElement = document.createElement('div');
            chatElement.className = 'chat-message';

            const time = timestamp ? new Date(timestamp).toLocaleTimeString() : new Date().toLocaleTimeString();

            chatElement.innerHTML = `
                <span style="color: #888; font-size: 0.8em;">[${time}]</span>
                <span class="username">${username}:</span>
                ${message}
            `;

            chatLog.appendChild(chatElement);
            chatLog.scrollTop = chatLog.scrollHeight;

            // Add pulse effect
            chatElement.classList.add('pulse');
            setTimeout(() => chatElement.classList.remove('pulse'), 1000);
        }

        // Connect on page load
        window.addEventListener('load', function () {
            connectWebSocket();
        });

        // Update client count periodically
        setInterval(function () {
            if (ws && ws.readyState === WebSocket.OPEN) {
                ws.send(JSON.stringify({ type: 'ping' }));
            }
        }, 30000);
    </script>
</body>

</html>