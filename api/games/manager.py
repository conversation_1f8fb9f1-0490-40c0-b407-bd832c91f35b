"""
Game manager for handling multiple game types and WebSocket integration
"""
import asyncio
import logging
from typing import Dict, Any, Optional
from .base import BaseGame, GamePhase
from ..services.database import db
from ..services.websocket import ws_service

logger = logging.getLogger(__name__)


class GameManager:
    """Manager for handling game lifecycle and WebSocket integration"""
    
    def __init__(self):
        self.current_game: Optional[BaseGame] = None
        self.game_types = {}  # Registry of available game types
        self.auto_restart = True
        self.restart_delay = 5  # seconds between games
        
    def register_game_type(self, game_name: str, game_class: type, default_config: Dict[str, Any] = None):
        """Register a new game type"""
        self.game_types[game_name] = {
            "class": game_class,
            "config": default_config or {}
        }
        logger.info(f"Registered game type: {game_name}")
    
    async def start_new_game(self, game_type: str = "blackjack", config: Dict[str, Any] = None) -> bool:
        """Start a new game of the specified type"""
        if game_type not in self.game_types:
            logger.error(f"Unknown game type: {game_type}")
            return False
        
        # End current game if running
        if self.current_game and self.current_game.state.phase != GamePhase.FINISHED:
            await self.end_current_game()
        
        # Create new game instance
        game_class = self.game_types[game_type]["class"]
        game_config = config or self.game_types[game_type]["config"]
        
        self.current_game = game_class(game_config)
        
        # Set up event broadcasting by storing reference to manager
        self.current_game._game_manager = self
        
        # Start join phase
        await self.current_game.start_join_phase()
        
        logger.info(f"Started new {game_type} game")
        return True
    
    async def _broadcast_game_event(self, event_type: str, data: Dict[str, Any]):
        """Broadcast game events to all connected WebSockets"""
        message = {
            "type": "game_event",
            "event_type": event_type,
            "data": data,
            "game_type": self.get_current_game_type(),
            "timestamp": data.get("timestamp") if "timestamp" in data else None
        }
        
        await ws_service.broadcast(message)
        logger.debug(f"Broadcasted game event: {event_type}")
    
    async def handle_chat_message(self, username: str, message: str):
        """Handle chat messages for game interactions"""
        if not self.current_game:
            return
        
        message = message.strip().lower()
        
        # Handle join command
        if message == "join" and self.current_game.state.phase == GamePhase.JOINING:
            success = self.current_game.join_game(username)
            if success:
                await self._broadcast_game_event("player_joined", {
                    "username": username,
                    "total_players": len(self.current_game.state.joined_players)
                })
        
        # Handle game actions
        elif self.current_game.state.phase == GamePhase.PLAYING:
            valid_actions = self.current_game.get_valid_actions()
            if message in valid_actions:
                success = self.current_game.add_vote(username, message)
                if success:
                    await self._broadcast_game_event("vote_cast", {
                        "username": username,
                        "action": message,
                        "current_votes": dict(self.current_game.state.current_votes),
                        "eligible_players": self.current_game.state.eligible_players,
                        "voting_mode": self.current_game.state.voting_mode.value
                    })
    
    async def force_action(self, action: str) -> bool:
        """Force execute an action (for testing or admin purposes)"""
        if not self.current_game or self.current_game.state.phase != GamePhase.PLAYING:
            return False
        
        if action not in self.current_game.get_valid_actions():
            return False
        
        # Clear current turn timer
        if self.current_game.turn_task:
            self.current_game.turn_task.cancel()
        
        # Execute action directly
        await self.current_game.end_turn()
        return True
    
    async def end_current_game(self):
        """End the current game and store results"""
        if not self.current_game:
            return
        
        await self.current_game.end_game()
        
        # Store game results in database
        try:
            db_connection = db.get_connection()
            game_id = await self.current_game.store_game_result(db_connection)
            
            await self._broadcast_game_event("game_stored", {
                "game_id": game_id,
                "message": "Game results stored in database"
            })
            
        except Exception as e:
            logger.error(f"Failed to store game results: {e}")
            await self._broadcast_game_event("storage_error", {
                "error": str(e),
                "message": "Failed to store game results"
            })
        
        # Schedule next game if auto-restart is enabled
        if self.auto_restart:
            await self._schedule_next_game()

    async def _handle_game_end(self):
        """Handle game end for auto-restart (called by game itself)"""
        if not self.current_game:
            return

        # Store game results in database
        try:
            db_connection = db.get_connection()
            game_id = await self.current_game.store_game_result(db_connection)

            await self._broadcast_game_event("game_stored", {
                "game_id": game_id,
                "message": "Game results stored in database"
            })

        except Exception as e:
            logger.error(f"Failed to store game results: {e}")
            await self._broadcast_game_event("storage_error", {
                "error": str(e),
                "message": "Failed to store game results"
            })

        # Schedule next game if auto-restart is enabled
        if self.auto_restart:
            await self._schedule_next_game()

    async def _schedule_next_game(self):
        """Schedule the next game after a delay"""
        await self._broadcast_game_event("next_game_scheduled", {
            "delay": self.restart_delay,
            "message": f"Next game starting in {self.restart_delay} seconds"
        })
        
        # Wait and start next game
        await asyncio.sleep(self.restart_delay)
        await self.start_new_game(self.get_current_game_type())
    
    def get_current_game_type(self) -> Optional[str]:
        """Get the type of the current game"""
        if not self.current_game:
            return None
        
        # Find game type by class
        for game_type, info in self.game_types.items():
            if isinstance(self.current_game, info["class"]):
                return game_type
        
        return "unknown"
    
    def get_current_state(self) -> Dict[str, Any]:
        """Get the current game state for WebSocket clients"""
        if not self.current_game:
            return {
                "game_type": None,
                "game_state": None,
                "phase": "waiting"
            }
        
        return {
            "game_type": self.get_current_game_type(),
            "game_state": self.current_game.get_state_dict(),
            "phase": self.current_game.state.phase.value
        }
    
    def get_status(self) -> Dict[str, Any]:
        """Get manager status information"""
        return {
            "current_game_type": self.get_current_game_type(),
            "phase": self.current_game.state.phase.value if self.current_game else "waiting",
            "registered_games": list(self.game_types.keys()),
            "auto_restart": self.auto_restart,
            "restart_delay": self.restart_delay
        }


# Global game manager instance
game_manager = GameManager()