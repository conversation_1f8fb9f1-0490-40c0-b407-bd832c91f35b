"""
Pump Plays Games API - Modular FastAPI application for multiple games
"""
import logging
from contextlib import asynccontextmanager
from fastapi import FastAP<PERSON>

from .core.config import settings
from .services.database import db
from .api.main import router as main_router
from .api.chat import router as chat_router

# Configure logging
logging.basicConfig(level=getattr(logging, settings.log_level.upper()))
logger = logging.getLogger(__name__)


# Lifespan context manager
@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    logger.info("Starting up Pump Plays Games API...")
    try:
        # Initialize database connection
        db.get_connection()
        logger.info("Database connection initialized")

        # Initialize game manager and register games
        from .games.manager import game_manager
        from .games.blackjack.game import BlackjackGame
        
        # Register blackjack game
        blackjack_config = {
            "join_phase_duration": settings.game_join_phase_duration,
            "turn_duration": settings.game_turn_duration,
            "voting_mode_chance": settings.blackjack_democracy_chance
        }
        game_manager.register_game_type("blackjack", BlackjackGame, blackjack_config)
        game_manager.auto_restart = settings.game_auto_restart
        game_manager.restart_delay = settings.game_restart_delay
        
        # Start the first game
        await game_manager.start_new_game("blackjack")
        
        logger.info("Game manager and services initialized")

    except Exception as e:
        logger.error(f"Failed to initialize application: {e}")

    yield

    # Shutdown
    logger.info("Shutting down Pump Plays Games API...")
    db.close_connection()


# Create FastAPI app
app = FastAPI(
    title="Pump Plays Games API",
    description="Modular API for Pump Plays Games - like Twitch Plays Pokemon but for various games!",
    version="1.0.0",
    lifespan=lifespan
)

# Include routers
app.include_router(main_router)
app.include_router(chat_router)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "api.main:app",
        host=settings.api_host,
        port=settings.api_port,
        workers=settings.api_workers,
        log_level=settings.log_level.lower(),
        reload=False
    )
