-- ==============================
-- Blackjack Plays Database Schema
-- ==============================

-- USERS: Players/viewers that participate
CREATE TABLE users (
    id BIGSERIAL PRIMARY KEY,
    username TEXT NOT NULL,
    wallet_address TEXT, -- optional Solana or other identifier
    created_at TIMESTAMP DEFAULT now(),
    updated_at TIMESTAMP DEFAULT now()
);

-- BLACKJACK GAMES: Stored only after game completes
CREATE TABLE blackjack_games (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMP DEFAULT now(),
    ended_at TIMESTAMP DEFAULT now(),
    seed TEXT, -- provable fairness seed if desired
    creator_fee_pool NUMERIC(18, 6) DEFAULT 0 -- funds allocated for this round
);

-- BLACKJACK EVENTS: Actual applied moves (no per-user)
CREATE TABLE blackjack_events (
    id BIGSERIAL PRIMARY KEY,
    game_id BIGINT REFERENCES blackjack_games (id) ON DELETE CASCADE,
    action TEXT CHECK (
        action IN (
            'deal',
            'hit',
            'stand',
            'double',
            'split',
            'end'
        )
    ) NOT NULL,
    card JSONB, -- {"rank":"K","suit":"hearts"} or null
    created_at TIMESTAMP DEFAULT now()
);

-- BLACKJACK VOTES: Stores all individual user decisions
CREATE TABLE blackjack_votes (
    id BIGSERIAL PRIMARY KEY,
    game_id BIGINT REFERENCES blackjack_games (id) ON DELETE CASCADE,
    user_id BIGINT REFERENCES users (id) ON DELETE CASCADE,
    mode TEXT CHECK (
        mode IN ('anarchy', 'democracy')
    ) NOT NULL,
    move TEXT CHECK (
        move IN (
            'hit',
            'stand',
            'double',
            'split'
        )
    ) NOT NULL,
    created_at TIMESTAMP DEFAULT now()
);

-- USER VIEWING DATA: tracks if a user was present at a timestamp
CREATE TABLE user_views (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES users (id) ON DELETE CASCADE,
    viewed_at TIMESTAMP DEFAULT now()
);

-- SOLANA CONFIG: manages game payout rules and balances
CREATE TABLE solana_config (
    id BIGSERIAL PRIMARY KEY,
    wallet_address TEXT NOT NULL,
    balance NUMERIC(36, 9) DEFAULT 0, -- current balance tracked
    game_stake_percent NUMERIC(5, 2) DEFAULT 10.00, -- % of balance used per game
    burn_percent NUMERIC(5, 2) DEFAULT 1.00, -- % burnt when house wins
    max_payout_cap NUMERIC(18, 6) DEFAULT 1000.00, -- cap per game payout
    created_at TIMESTAMP DEFAULT now(),
    updated_at TIMESTAMP DEFAULT now()
);

-- USER WINNERS: which users won and how much
CREATE TABLE user_winners (
    id BIGSERIAL PRIMARY KEY,
    game_id BIGINT REFERENCES blackjack_games (id) ON DELETE CASCADE,
    user_id BIGINT REFERENCES users (id) ON DELETE CASCADE,
    payout NUMERIC(18, 6) NOT NULL,
    result TEXT CHECK (
        result IN (
            'dealer_busted',
            'player_busted',
            'dealer_win',
            'player_win',
            'push'
        )
    ) NOT NULL,
    created_at TIMESTAMP DEFAULT now()
);

-- INDEXES for speed
CREATE INDEX idx_events_game_id ON blackjack_events (game_id);

CREATE INDEX idx_votes_game_id ON blackjack_votes (game_id);

CREATE INDEX idx_views_user_id ON user_views (user_id);

CREATE INDEX idx_winners_user_id ON user_winners (user_id);

-- Unique usernames in users
CREATE UNIQUE INDEX idx_users_username ON users (username);